plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.serialization)
}


android {
    namespace = "com.hwb.timecontroller"
    compileSdk = 36

    defaultConfig {
        applicationId = "com.hwb.timecontroller"
        minSdk = 30
        targetSdk = 36
        versionCode = 8
        versionName = "1.0.8"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    // 签名配置
    signingConfigs {
        create("meijuyunben") {
            storeFile =  file("../meijuyunben.jks")
            storePassword = "123456"
            keyAlias = "key0"
            keyPassword = "123456"
        }
    }

    buildTypes {
        debug {
            // Debug版本不隐藏最近任务，方便调试
            manifestPlaceholders["excludeFromRecents"] = "false"
            signingConfig = signingConfigs.getByName("meijuyunben")
        }
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            // Release版本隐藏最近任务
            manifestPlaceholders["excludeFromRecents"] = "true"
            // 使用自定义签名
            signingConfig = signingConfigs.getByName("meijuyunben")
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_21
        targetCompatibility = JavaVersion.VERSION_21
    }
    kotlin {
        jvmToolchain(21)
    }

    lint {
        disable += setOf("MissingSuperCall", "GestureBackNavigation")
    }
    buildFeatures {
        viewBinding = true
        buildConfig = true
        aidl = true
    }
}

dependencies {
    implementation(libs.androidx.activity.ktx)
    implementation(libs.androidx.lifecycle.livedata.ktx)
    implementation(libs.androidx.lifecycle.viewmodel.ktx)

    // Kotlin协程支持
    implementation(libs.kotlinx.coroutines.android)
    implementation(libs.kotlinx.coroutines.core)

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    implementation(libs.androidx.cardview)
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    implementation(libs.mmkv)

    implementation(libs.dialogx)
    implementation(libs.toaster)
    implementation(libs.binding)

    // 图片加载
    implementation(libs.coil)

    // UUID生成库
    implementation(libs.uuid.creator)

    // 二维码生成库
    implementation(libs.core)

    // Ktor网络框架 - 统一使用OkHttp引擎
    implementation(libs.ktor.client.okhttp)
    implementation(libs.ktor.client.websockets)
    implementation(libs.ktor.client.content.negotiation)
    implementation(libs.ktor.serialization.kotlinx.json)
    implementation(libs.ktor.client.logging)

    // XUpdate版本更新框架
    implementation(libs.xupdate)

    //xLog
    implementation(libs.xlog)
}