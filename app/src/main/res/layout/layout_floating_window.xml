<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <!-- 悬浮窗主体区域（包含内容、箭头和警告） -->
    <LinearLayout
        android:id="@+id/layout_main"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 主体内容区域 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_floating_main"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp"
            android:paddingVertical="18dp">

            <!-- 主页图标和文本 -->
            <LinearLayout
                android:id="@+id/btn_home"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:background="@android:color/transparent"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:contentDescription="回到主页"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_home" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="回到主页"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

            </LinearLayout>

            <!-- 倒计时文本和标签 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_countdown"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:minWidth="80dp"
                    android:text="00:00"
                    android:textColor="@color/white"
                    android:textSize="36sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="剩余时间"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

            </LinearLayout>

            <!-- 二维码图标和文本 -->
            <LinearLayout
                android:id="@+id/btn_qr_code"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:background="@android:color/transparent"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:contentDescription="二维码"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_qr_code" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="扫码续租"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

            </LinearLayout>

        </LinearLayout>

        <!-- 箭头指示器 -->
        <FrameLayout
            android:id="@+id/btn_arrow"
            android:layout_width="70dp"
            android:layout_height="30dp"
            android:layout_gravity="center"
            android:layout_marginTop="-13dp"
            android:background="@drawable/bg_floating_arrow"
            android:clickable="true"
            android:focusable="true">

            <View
                android:id="@+id/iv_arrow"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:background="@drawable/ic_arrow_down" />

        </FrameLayout>

        <!-- 二维码展示区域 - 默认隐藏，点击扫码续租时显示 -->
        <FrameLayout
            android:id="@+id/layout_qr_code"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="8dp"
            android:visibility="gone"
            tool:visibility="visible">

            <FrameLayout
                android:layout_width="180dp"
                android:layout_height="180dp"
                android:layout_gravity="center"
                android:layout_margin="18dp"
                android:background="#6A40CB"
                android:padding="18dp">

                <ImageView
                    android:id="@+id/iv_qr_code"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY" />
            </FrameLayout>

            <!-- 关闭按钮 - 右上角 -->
            <View
                android:id="@+id/btn_close_qr"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_gravity="top|end"
                android:background="@drawable/ic_floating_close" />


        </FrameLayout>

        <!-- 警告显示区域 - 包含在主体区域内，跟着一起移动 -->
        <LinearLayout
            android:id="@+id/layout_warning"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="8dp"
            android:background="@drawable/warning_background"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="12dp"
            android:paddingVertical="6dp"
            android:visibility="gone"
            tool:visibility="visible">

            <ImageView
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_marginEnd="8dp"
                android:src="@drawable/ic_lock" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/governance_warning"
                android:textColor="@color/lock_accent_orange"
                android:textSize="13sp"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

</FrameLayout>
