<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_login">


    <ImageView
        android:id="@+id/ivLogo"
        android:layout_width="500dp"
        android:layout_height="500dp"
        android:layout_gravity="center"
        android:src="@drawable/ic_login_logo"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/flQrcode"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/flQrcode"
        android:layout_width="280dp"
        android:layout_height="280dp"
        android:layout_gravity="center"
        android:layout_marginBottom="24dp"
        android:background="@drawable/qr_card_background"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="@id/ivLogo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/ivLogo"
        app:layout_constraintTop_toTopOf="@id/ivLogo">

        <ImageView
            android:id="@+id/iv_qr_code"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY" />
    </FrameLayout>


    <TextView
        android:id="@+id/btnBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="50dp"
        android:background="@drawable/bg_login_back"
        android:gravity="center"
        android:paddingHorizontal="100dp"
        android:paddingVertical="10dp"
        android:text="@string/cancel_login"
        android:textColor="@color/lock_on_primary"
        android:textSize="26sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivLogo" />

    <TextView
        android:id="@+id/tv_payment_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:text="@string/login_status_waiting"
        android:textColor="@color/lock_text_primary"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="@id/flQrcode"
        app:layout_constraintStart_toStartOf="@id/flQrcode"
        app:layout_constraintTop_toBottomOf="@id/flQrcode" />

    <TextView
        android:layout_width="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_payment_status"
        app:layout_constraintStart_toStartOf="@id/tv_payment_status"
        app:layout_constraintEnd_toEndOf="@id/tv_payment_status"
        android:text="低至6.6元起"
        android:textStyle="bold"
        android:layout_marginTop="5dp"
        android:textColor="@color/white"
        android:textSize="36sp"
        android:layout_height="wrap_content"/>
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_countdown"
        android:gravity="center"
        android:paddingHorizontal="50dp"
        android:paddingVertical="15dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="无操作"
            android:textColor="@color/white"
            android:textSize="24sp" />

        <TextView
            android:id="@+id/tv_countdown_seconds"
            android:layout_width="wrap_content"

            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:gravity="center"
            android:text="180"
            android:textColor="#FFE361"
            android:textSize="24sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="秒后将回到首页"
            android:textColor="@color/white"
            android:textSize="24sp" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
