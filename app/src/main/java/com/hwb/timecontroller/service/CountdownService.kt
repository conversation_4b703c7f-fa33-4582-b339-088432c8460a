package com.hwb.timecontroller.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Intent
import android.os.CountDownTimer
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.elvishew.xlog.XLog
import com.hwb.timecontroller.R
import com.hwb.timecontroller.activity.AppDataCleanupActivity
import com.hwb.timecontroller.activity.LoginActivity
import com.hwb.timecontroller.activity.MainActivity
import com.hwb.timecontroller.business.BalancePollingManager
import com.hwb.timecontroller.business.CountdownManager
import com.hwb.timecontroller.business.UserManager
import com.hwb.timecontroller.service.keepalive.KeepAliveCapable
import com.hwb.timecontroller.service.keepalive.MutualKeepAliveManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import java.util.concurrent.TimeUnit

/**
 * 计时服务
 * 支持互相保活机制
 */
class CountdownService : Service(), KeepAliveCapable {

    companion object {
        const val NOTIFICATION_ID = 1001
        const val CHANNEL_ID = "countdown_channel"
        const val ACTION_UNLOCK_DEVICE = "com.hwb.timecontroller.ACTION_UNLOCK_DEVICE"

        private var serviceInstance: CountdownService? = null

        /**
         * 暂停倒计时（静态方法）
         */
        fun pauseCountdownStatic() {
            serviceInstance?.pauseCountdown()
        }

        /**
         * 恢复倒计时（静态方法）
         */
        fun resumeCountdownStatic() {
            serviceInstance?.resumeCountdown()
        }
    }

    private var countDownTimer: CountDownTimer? = null
    private lateinit var notificationManager: NotificationManager

    // 保活管理器
    private lateinit var keepAliveManager: MutualKeepAliveManager

    // 协程作用域
    private val serviceScope = CoroutineScope(Dispatchers.Main + Job())
    private var countdownObserverJob: Job? = null

    // 分钟计数器，用于每分钟扣款
    private var secondsCounter = 0
    // 已扣款的分钟数，用于精确计算最后扣款
    private var deductedMinutes = 0

    // 暂停相关变量
    private var isPaused = false
    private var pausedRemainingTime: Long = 0L
    private var pausedStartTime: Long = 0L
    private var totalPausedTime: Long = 0L

    // 倒计时开始时间和总时长，用于计算最后一段时间的扣款
    private var countdownStartTime: Long = 0L
    private var countdownTotalDuration: Long = 0L

    // 用于检测外部时间更新
    private var lastKnownRemainingTime: Long = 0L

    override fun onCreate() {
        super.onCreate()

        XLog.d("倒计时服务开始创建")
        serviceInstance = this

        // 初始化保活管理器
        keepAliveManager = MutualKeepAliveManager(
            context = this,
            currentServiceClass = CountdownService::class.java,
            serviceDisplayName = "倒计时服务"
        )

        // 注册需要保活的伙伴服务
        keepAliveManager.registerPartnerServices(
            FloatingWindowService::class.java,
            AppLifecycleManagerService::class.java,
            AppLifecycleAccessibilityService::class.java,
            CheckService::class.java
        )

        // 启动保活
        keepAliveManager.startKeepAlive()

        notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        createNotificationChannel()
        startForeground(NOTIFICATION_ID, createNotification(0))

        // 监听CountdownManager的状态变化
        observeCountdownState()

        XLog.d("倒计时服务创建完成")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val duration = intent?.getLongExtra("duration", 0L) ?: 0L

        if (duration > 0) {
            // 检查是否已有倒计时在运行
            if (countDownTimer != null) {
                // 如果当前倒计时时间与新请求的时间相同，不需要重启
                val currentRemainingTime = CountdownManager.getRemainingTime()
                if (kotlin.math.abs(currentRemainingTime - duration) < 1000L) {
                    XLog.d("倒计时已在运行且时间相同，无需重启: ${duration}ms")
                    return START_STICKY
                } else {
                    XLog.d("检测到已有倒计时在运行但时间不同，重新启动: 当前=${currentRemainingTime}ms, 新=${duration}ms")
                    countDownTimer?.cancel()
                    countDownTimer = null
                }
            }

            // 使用CountdownManager开始倒计时
            CountdownManager.startCountdown(duration)
            startCountdown(duration)
            listenForUnlockCommand()
        }

        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    private fun createNotificationChannel() {
        val channel = NotificationChannel(
            CHANNEL_ID,
            getString(R.string.notification_channel_name),
            NotificationManager.IMPORTANCE_LOW
        ).apply {
            description = getString(R.string.notification_channel_description)
            setShowBadge(false)
        }
        notificationManager.createNotificationChannel(channel)
    }

    private fun createNotification(remainingTimeMillis: Long): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent, PendingIntent.FLAG_IMMUTABLE
        )

        val timeText = if (CountdownManager.isCountdownRunning()) {
            val minutes = TimeUnit.MILLISECONDS.toMinutes(remainingTimeMillis)
            val seconds = TimeUnit.MILLISECONDS.toSeconds(remainingTimeMillis) % 60
            getString(R.string.notification_countdown_text, minutes, seconds)
        } else {
            getString(R.string.notification_device_locked)
        }

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(getString(R.string.app_name))
            .setContentText(timeText)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .build()
    }

    private fun startCountdown(duration: Long) {
        countDownTimer?.cancel()

        // 重置秒数计数器和已扣款分钟数
        secondsCounter = 0
        deductedMinutes = 0

        // 记录倒计时开始时间和总时长
        countdownStartTime = System.currentTimeMillis()
        countdownTotalDuration = duration

        // 重置暂停相关变量
        totalPausedTime = 0L
        pausedStartTime = 0L
        isPaused = false
        lastKnownRemainingTime = duration

        countDownTimer = object : CountDownTimer(duration, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                // 如果暂停，不执行任何操作（包括不计费）
                if (isPaused) {
                    pausedRemainingTime = millisUntilFinished
                    return
                }

                // 更新CountdownManager中的剩余时间
                CountdownManager.updateRemainingTime(millisUntilFinished)

                // 更新通知显示剩余时间
                val notification = createNotification(millisUntilFinished)
                notificationManager.notify(NOTIFICATION_ID, notification)

                // 每分钟扣款逻辑（只在非暂停状态下计数和扣款）
                secondsCounter++
                if (secondsCounter >= 60) {
                    secondsCounter = 0
                    // 异步执行扣款，不阻塞倒计时
                    serviceScope.launch {
                        val success = performMinutelyDeduction()
                        if (success) {
                            deductedMinutes++ // 扣款成功后增加已扣款分钟数
                        }
                    }
                }
            }

            override fun onFinish() {
                // 在倒计时结束时，处理最后一段时间的扣款
                serviceScope.launch {
                    performFinalDeduction()
                }

                // 通知CountdownManager倒计时结束
                CountdownManager.finishCountdown()
                lockDevice()
            }
        }.start()
    }

    private fun lockDevice() {
        XLog.d("设备锁定开始")

        // 设置CountdownManager中的设备锁定状态
        CountdownManager.setDeviceLocked(true)

        // 更新通知显示设备已锁定
        val notification = createNotification(0)
        notificationManager.notify(NOTIFICATION_ID, notification)

        // 【甲方测试需求】倒计时结束后启动AppDataCleanupActivity
        XLog.d("倒计时结束，按甲方要求启动AppDataCleanupActivity")
        val intent = Intent(this, AppDataCleanupActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        startActivity(intent)

        // 【注释掉原来的逻辑】根据用户登录状态和余额情况决定跳转到哪个Activity
        /*
        if (UserManager.isUserLoggedIn()) {
            // 用户已登录，检查余额是否足够
            if (BalancePollingManager.isBalanceSufficientForMinutes(1.0)) {
                XLog.d("用户已登录且余额充足，但倒计时结束，跳转到UserInfoActivity")
                // 启动用户信息Activity（倒计时结束入口）
                com.hwb.timecontroller.activity.UserInfoActivity.start(
                    this,
                    com.hwb.timecontroller.activity.UserInfoActivity.UserInfoEntryType.COUNTDOWN_ENDED_ENTRY
                )
            } else {
                XLog.d("用户已登录但余额不足，跳转到UserInfoActivity")
                // 启动用户信息Activity（倒计时结束入口）
                com.hwb.timecontroller.activity.UserInfoActivity.start(
                    this,
                    com.hwb.timecontroller.activity.UserInfoActivity.UserInfoEntryType.COUNTDOWN_ENDED_ENTRY
                )
            }
        } else {
            XLog.d("用户未登录，跳转到LoginActivity")
            // 启动锁定Activity
            val intent = Intent(this, LoginActivity::class.java)
            // 移除FLAG_ACTIVITY_CLEAR_TOP避免影响EmptyActivity的startLockTask状态
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP
            // 设置入口类型为倒计时结束
            intent.putExtra(
                LoginActivity.EXTRA_ENTRY_TYPE,
                LoginActivity.LockActivityEntryType.COUNTDOWN_ENDED.name
            )
            startActivity(intent)
        }
        */
    }

    private fun listenForUnlockCommand() {
        // 占位函数：监听网络解锁指令
        // 这里可以实现网络监听逻辑，例如：
        // - FCM推送消息
        // - WebSocket连接
        // - HTTP轮询
        // - 本地广播接收器

        // 示例：模拟10秒后自动解锁（仅用于测试）
        // 实际使用时应该删除这部分代码
        /*
        Handler(Looper.getMainLooper()).postDelayed({
            unlockDevice()
        }, 10000) // 10秒后自动解锁
        */
    }

    fun unlockDevice() {
        XLog.d("设备解锁开始")

        // 停止倒计时
        countDownTimer?.cancel()

        // 取消CountdownManager中的倒计时
        CountdownManager.cancelCountdown()

        // 发送广播通知LockActivity关闭
        val intent = Intent(ACTION_UNLOCK_DEVICE)
        intent.setPackage(packageName)
        sendBroadcast(intent)

        // 停止服务
        stopForeground(STOP_FOREGROUND_REMOVE)
        stopSelf()
    }

    /**
     * 监听CountdownManager的状态变化
     */
    private fun observeCountdownState() {
        countdownObserverJob = serviceScope.launch {
            CountdownManager.countdownData.collect { data ->
                // 只在整分钟时输出日志，减少日志量
                val remainingSeconds = (data.remainingTimeMillis / 1000) % 60
                if (remainingSeconds == 0L || data.remainingTimeMillis == 0L) {
                    XLog.d("倒计时数据变化: 剩余时间: ${data.remainingTimeMillis}")
                }

                // 更新通知（虽然在lockTaskMode下不显示，但保持兼容性）
                val notification = createNotification(data.remainingTimeMillis)
                notificationManager.notify(NOTIFICATION_ID, notification)

                // 检查外部时间更新：如果时间显著增加，说明可能是充值等外部更新
                if (CountdownManager.isCountdownRunning() && countDownTimer != null && !isPaused) {
                    val currentTime = data.remainingTimeMillis

                    // 如果时间比上次记录的时间增加超过30秒，说明是外部更新（如充值）
                    // 正常倒计时时间只会减少，增加说明是外部干预
                    if (currentTime > lastKnownRemainingTime + 30000L) {
                        XLog.d("检测到时间显著增加，可能是充值等外部更新，重新同步倒计时: 上次=${lastKnownRemainingTime}ms, 当前=${currentTime}ms")
                        restartCountdownTimer(currentTime)
                    }

                    // 更新记录的时间
                    lastKnownRemainingTime = currentTime
                }

                // 检查倒计时是否结束（只有在用户登录时才触发锁定流程）
                if (CountdownManager.isCountdownFinished() && UserManager.isUserLoggedIn()) {
                    XLog.d("倒计时结束且用户已登录，启动锁定流程")
                    lockDevice()
                }
            }
        }
    }

    /**
     * 执行每分钟扣款操作
     * @return 扣款是否成功
     */
    private suspend fun performMinutelyDeduction(): Boolean {
        try {
            // 检查用户是否已登录
            if (!UserManager.isUserLoggedIn()) {
                XLog.d("用户未登录，跳过扣款")
                return false
            }

            // 检查倒计时是否还在运行
            if (!CountdownManager.isCountdownRunning()) {
                XLog.d("倒计时未运行，跳过扣款")
                return false
            }

            // 获取当前用户ID
            val currentUser = UserManager.getUserInfo()
            val userId = currentUser?.userId
            if (userId.isNullOrEmpty()) {
                XLog.w("用户ID为空，无法执行扣款")
                return false
            }

            // 获取每分钟扣款金额
            val perMinuteCost = BalancePollingManager.getPerMinuteCost()
            if (perMinuteCost == null || perMinuteCost <= 0) {
                XLog.w("无法获取扣款金额或金额无效: $perMinuteCost")
                return false
            }

            XLog.d("开始执行每分钟扣款: userId=$userId, amount=$perMinuteCost")

            // 执行扣款，禁用自动刷新余额（我们手动处理）
            val result =
                BalancePollingManager.deductMoney(userId, perMinuteCost, autoRefreshBalance = false)

            if (result.isSuccess) {
                val response = result.getOrNull()
                if (response?.code == 200 && response.rspdata != null) {
                    val consumedTotal = response.rspdata.consumedTotal
                    val newBalance = response.rspdata.newBalance
                    XLog.d("每分钟扣款成功: 扣款金额=$perMinuteCost, 实际消费=$consumedTotal, 新余额=$newBalance")

                    // 使用新余额和缓存的比例直接计算剩余时间，避免额外API调用
                    if (newBalance != null && newBalance > 0) {
                        val newTimeInMinutes = newBalance / perMinuteCost
                        val newTimeInSeconds = (newTimeInMinutes * 60).toInt()
                        val newTimeInMillis = newTimeInSeconds * 1000L

                        XLog.d("基于新余额计算剩余时间: 余额=$newBalance, 比例=$perMinuteCost, 新时间=${newTimeInMinutes}分钟")

                        // 直接更新CountdownManager，不触发余额查询
                        CountdownManager.updateRemainingTime(newTimeInMillis)

                        // 更新BalancePollingManager中的余额数据缓存
                        BalancePollingManager.updateBalanceCache(newBalance, newTimeInSeconds)

                        // 直接重启倒计时定时器，使用扣款后的新时间，避免被其他数据源覆盖
                        XLog.d("扣款成功后直接重启倒计时定时器: ${newTimeInMillis}ms")
                        restartCountdownTimer(newTimeInMillis)
                    } else if (newBalance != null && newBalance <= 0) {
                        XLog.w("余额已用完: $newBalance，停止倒计时")
                        CountdownManager.finishCountdown()
                    }
                    return true // 扣款成功
                } else {
                    XLog.w("扣款失败:msg=${response?.msg}, code=${response?.code}")
                    // 检查是否为余额不足的情况
                    val isInsufficientBalance = response?.code == 300 ||
                            response?.msg?.contains("余额不足") == true ||
                            response?.msg?.contains("insufficient") == true ||
                            response?.msg?.contains("无数据") == true

                    if (isInsufficientBalance) {
                        XLog.w("余额不足或无数据，停止倒计时")
                        CountdownManager.finishCountdown()
                    } else {
                        XLog.w("扣款失败但非余额不足，继续倒计时: ${response?.msg}")
                    }
                    return false // 扣款失败
                }
            } else {
                val error = result.exceptionOrNull()
                XLog.e("扣款操作失败，继续倒计时", error)
                return false // 扣款失败
            }

        } catch (e: Exception) {
            XLog.e("执行每分钟扣款时发生异常，继续倒计时", e)
            return false // 扣款异常
        }
    }


    override fun onDestroy() {
        try {
            XLog.d("倒计时服务开始销毁")
            serviceInstance = null

            countDownTimer?.cancel()

            // 取消协程作用域
            countdownObserverJob?.cancel()
            serviceScope.cancel()

            // 停止保活
            if (::keepAliveManager.isInitialized) {
                keepAliveManager.onServiceDestroyed()
            }

            // 重置CountdownManager状态
            CountdownManager.resetCountdown()

            XLog.d("倒计时服务销毁完成")

        } catch (e: Exception) {
            XLog.e("倒计时服务销毁失败", e)
        } finally {
            super.onDestroy()
        }
    }

    // 实现KeepAliveCapable接口
    override fun getKeepAliveManager(): MutualKeepAliveManager {
        return keepAliveManager
    }

    override fun onPartnerServiceDied(serviceClass: Class<out Service>) {
        super.onPartnerServiceDied(serviceClass)
        XLog.w("倒计时服务检测到伙伴服务死亡: ${serviceClass.simpleName}")
    }

    override fun onPartnerServiceRestarted(serviceClass: Class<out Service>) {
        super.onPartnerServiceRestarted(serviceClass)
        XLog.i("倒计时服务检测到伙伴服务重启: ${serviceClass.simpleName}")
    }

    /**
     * 执行最后一段时间的扣款操作
     */
    private suspend fun performFinalDeduction() {
        try {
            // 检查用户是否已登录
            if (!UserManager.isUserLoggedIn()) {
                XLog.d("用户未登录，跳过最后扣款")
                return
            }

            // 获取当前用户ID
            val currentUser = UserManager.getUserInfo()
            val userId = currentUser?.userId
            if (userId.isNullOrEmpty()) {
                XLog.w("用户ID为空，无法执行最后扣款")
                return
            }

            // 获取每分钟扣款金额
            val perMinuteCost = BalancePollingManager.getPerMinuteCost()
            if (perMinuteCost == null || perMinuteCost <= 0) {
                XLog.w("无法获取扣款金额或金额无效: $perMinuteCost")
                return
            }

            // 计算实际使用时间（排除暂停时间）
            val currentTime = System.currentTimeMillis()
            var actualUsedTimeMillis = currentTime - countdownStartTime - totalPausedTime

            // 如果当前正在暂停，还需要减去当前暂停的时间
            if (isPaused && pausedStartTime > 0) {
                actualUsedTimeMillis -= (currentTime - pausedStartTime)
            }

            val actualUsedMinutes = actualUsedTimeMillis / 60000.0

            // 计算已扣款的分钟数（使用精确的已扣款分钟数）
            val alreadyDeductedMinutes = deductedMinutes.toDouble()

            // 计算未扣款的时间
            val undeductedMinutes = actualUsedMinutes - alreadyDeductedMinutes

            XLog.d("最后扣款计算: 实际使用=${String.format("%.2f", actualUsedMinutes)}分钟, " +
                    "已扣款=${alreadyDeductedMinutes}分钟, " +
                    "未扣款=${String.format("%.2f", undeductedMinutes)}分钟, " +
                    "暂停时间=${totalPausedTime}ms")

            // 如果未扣款时间超过10秒，则进行扣款（四舍五入到分钟）
            if (undeductedMinutes > 0.17) { // 0.17分钟 ≈ 10秒
                // 四舍五入到最近的分钟
                val roundedUndeductedMinutes = kotlin.math.round(undeductedMinutes).toDouble()

                // 检查四舍五入后是否大于0，避免0金额扣款
                if (roundedUndeductedMinutes > 0) {
                    val finalDeductionAmount = roundedUndeductedMinutes * perMinuteCost

                    XLog.d("开始执行最后扣款: userId=$userId, 未扣款时间=${String.format("%.2f", undeductedMinutes)}分钟, " +
                            "四舍五入=${roundedUndeductedMinutes}分钟, 扣款金额=${String.format("%.2f", finalDeductionAmount)}")

                    // 执行最后扣款，禁用自动刷新余额
                    val result = BalancePollingManager.deductMoney(userId, finalDeductionAmount, autoRefreshBalance = false)

                    if (result.isSuccess) {
                        val response = result.getOrNull()
                        if (response?.code == 200) {
                            XLog.d("最后扣款成功: ${response.msg}")
                        } else {
                            XLog.w("最后扣款失败: ${response?.msg}")
                        }
                    } else {
                        val error = result.exceptionOrNull()
                        XLog.e("最后扣款操作失败", error)
                    }
                } else {
                    XLog.d("四舍五入后扣款时间为0分钟，跳过最后扣款")
                }
            } else {
                XLog.d("未扣款时间不足10秒，跳过最后扣款")
            }

        } catch (e: Exception) {
            XLog.e("执行最后扣款时发生异常", e)
        }
    }

    /**
     * 暂停倒计时和扣款
     */
    fun pauseCountdown() {
        if (!isPaused) {
            isPaused = true
            pausedStartTime = System.currentTimeMillis()
            XLog.d("倒计时已暂停")
        }
    }

    /**
     * 恢复倒计时和扣款
     */
    fun resumeCountdown() {
        if (isPaused) {
            isPaused = false
            // 累计暂停时间
            if (pausedStartTime > 0) {
                totalPausedTime += System.currentTimeMillis() - pausedStartTime
                pausedStartTime = 0L
            }
            XLog.d("倒计时已恢复，累计暂停时间: ${totalPausedTime}ms")
        }
    }

    /**
     * 重新启动倒计时定时器（用于外部时间更新同步）
     * @param newDurationMillis 新的倒计时时长
     */
    private fun restartCountdownTimer(newDurationMillis: Long) {
        try {
            // 取消当前定时器
            countDownTimer?.cancel()

            // 重置相关状态但保持暂停状态和累计时间
            val wasPaused = isPaused
            val savedPausedStartTime = pausedStartTime
            val savedTotalPausedTime = totalPausedTime
            val savedDeductedMinutes = deductedMinutes

            // 启动新的定时器
            startCountdown(newDurationMillis)

            // 恢复暂停状态和累计时间
            isPaused = wasPaused
            pausedStartTime = savedPausedStartTime
            totalPausedTime = savedTotalPausedTime
            deductedMinutes = savedDeductedMinutes

            XLog.d("倒计时定时器已重新启动: ${newDurationMillis}ms, 暂停状态: $isPaused")

        } catch (e: Exception) {
            XLog.e("重新启动倒计时定时器失败", e)
        }
    }

}
